package colvir

import (
	"context"
	"net/http"
	"time"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

type (
	ColvirProvider interface {
		RequestTaxPayer(ctx context.Context, iin string) (*entity.FindTaxPayerResponse, error)
		RequestCreateClient(ctx context.Context, request *entity.CreateClientRequest) (*entity.CreateClientResp, error)
		RequestUpdateClient(ctx context.Context, request *entity.UpdateClientRequest) (*entity.UpdateClientResp, error)
		RequestGetClient(ctx context.Context, req *entity.GetClientRequest) (*entity.GetClientResponse, error)
		RequestGetClientIP(ctx context.Context, req *entity.GetClientIPRequest) (*entity.GetClientIPResult, error)
		RequestGetClientLoans(ctx context.Context, req *entity.GetClientLoansRequest) (*entity.GetClientLoansResp, error)
		RequestGetClientDeposits(ctx context.Context, req *entity.GetClientDepositsRequest) (*entity.GetClientDepositsResponse, error)
		GetClients(ctx context.Context) error
		RequestOpenClientCard(ctx context.Context, req *entity.OpenClientCardRequest) (*entity.OpenClientCardResponse, error)
		RequestGetClientCard(ctx context.Context, req *entity.GetClientCardRequest) (*entity.GetClientCardResponse, error)
		RequestGetClientIPCard(ctx context.Context, req *entity.GetClientIPCardRequest) (*entity.GetClientIPCardResponse, error)
		RequestLoadAccountTransactions(ctx context.Context, reqBody *entity.LoadAccountTransactionsRequest) (*entity.LoadAccountTransactionsResponse, error)
		RequestFindClient(ctx context.Context, request entity.FindClientReq) (*entity.FindClientResp, error)
		RequestFindClientAccountsList(ctx context.Context, req entity.RequestFindClientAccountsListReq) (*entity.FindClientAccountsListResponse, error)
		RequestCheckDomesticPayment(ctx context.Context, r *entity.CheckDomesticPaymentRequest) (
			*entity.CheckDomesticPaymentResponse, error)
		RequestExecuteDomesticPayment(ctx context.Context, r *entity.ExecuteDomesticPaymentRequest) (
			*entity.ExecuteDomesticPaymentResponse, error)
		RequestCheckDomesticMassPayment(ctx context.Context, r *entity.CheckDomesticMassPaymentRequest) (
			*entity.CheckDomesticMassPaymentResponse, error)
		RequestExecuteDomesticMassPayment(ctx context.Context, r *entity.ExecuteDomesticMassPaymentRequest) (
			*entity.ExecuteDomesticMassPaymentResponse, error)
		LoadClientBankRelationLink(ctx context.Context, clientCode string) (*entity.LoadClientBankRelationLinkResponseEnvelope, error)
		RequestLoadDomainValues(ctx context.Context, domainValueCode string) (*entity.LoadDomainValuesResponse, error)
		RequestLoadDomainHierarchyValues(
			ctx context.Context,
			domainValueCode string,
		) (*entity.LoadDomainHierarchyValuesResponse, error)
		RequestLoadBankList(ctx context.Context) (*entity.LoadBankListResponse, error)
		RequestCreateClientAgreement(
			ctx context.Context, request entity.CreateClientAgreementRequest,
		) (*entity.CreateClientAgreementResp, error)
		RequestCheckClientAgreement(
			ctx context.Context, request entity.CheckClientAgreementRequest,
		) (*entity.CheckClientAgreementResponse, error)
		RequestLoadBankHolidays(ctx context.Context, startDate, endDate time.Time) (*entity.LoadBankHolidaysResponse, error)
		RequestCreateClientSMEIP(ctx context.Context, request *entity.CreateClientIPRequest) (*entity.CreateClientIPResp, error)
		RequestUpdateClientSMEIP(ctx context.Context, request *entity.UpdateClientIPRequest) (*entity.UpdateClientIPResp, error)
		LoanCalcLoadPreSchedule(ctx context.Context, req *entity.LoanCalcLoadPreScheduleReq) (*entity.LoanCalcLoadPreScheduleResp, error)
		RequestSaveLoan(ctx context.Context, request *entity.SaveLoanRequest) (*entity.SaveLoanResponse, error)
		RequestLoansCalculateSchedule(ctx context.Context, request *entity.LoansCalculateScheduleReq) (*entity.LoansCalculateScheduleResp, error)
		RequestLoansCalculateTotalCost(ctx context.Context, request *entity.LoansCalculateTotalCostReq) (*entity.LoansCalculateTotalCostResp, error)
		GetLoanDetails(ctx context.Context, request *entity.GetLoanDetailsReq) (*entity.GetLoanDetailsResp, error)
		GetLoanSchedule(ctx context.Context, req *entity.GetLoanScheduleReq) (*entity.GetLoanScheduleRespWrapper, error)
		GetCreditContractDetails(ctx context.Context, req *entity.GetCreditContractDetailsReqBodyLoadLoanDetailsRequest) (*entity.GetCreditContractDetailsRespBodyLoadLoanDetailsResponse, error)
		RequestLoadDomesticPaymentStatus(ctx context.Context, paymentIDs []string, clientType string) (*entity.LoadDomesticPaymentStatusResponse, error)
		LoadLoanAgreementDetails(ctx context.Context, request *entity.LoadLoanAgreementDetailsReq) (*entity.LoadLoanAgreementDetailsResp, error)
		RequestProvidingLoan(ctx context.Context, req *entity.ProvidingLoanReq) (*entity.ProvidingLoanResp, error)
		GetLoanAmounts(ctx context.Context, req *entity.GetLoanAmountsReq) (*entity.GetLoanAmountsResp, error)
		GetMissedPayments(ctx context.Context, req *entity.GetMissedPaymentsReq) (*entity.GetMissedPaymentsResp, error)
		GetAllLoans(ctx context.Context, req *entity.GetAllLoansReq) (*entity.GetAllLoansRespWrapper, error)
		RepayLoanEarly(ctx context.Context, request *entity.RepayLoanEarlyReq) (*entity.RepayLoanEarlyResp, error)
		RequestLoanRegisterShd(ctx context.Context, request *entity.LoanRegisterShdReq) (*entity.LoanRegisterShdResp, error)
		GetStatusOperDate(ctx context.Context, date string) (*entity.RespStatusOperDate, error)
		RequestOpenDeposit(ctx context.Context, req *entity.OpenDepositRequest) (*entity.OpenDepositResponse, error)
		LoadOverdueLoanPaymentReport(ctx context.Context, request *entity.LoadOverdueLoanPaymentReport) (*entity.LoadOverdueLoanPaymentReportResponse, error)
		LoadUGDDictionary(ctx context.Context, request *entity.LoadColvirReportDataElemRequest) (*entity.LoadColvirReportDataElemResponse, error)

		// Методы управления лимитами
		RequestCreateLimit(ctx context.Context, request *entity.CreateLimitReq) (*entity.CreateLimitResp, error)
		RequestCloseLimit(ctx context.Context, request *entity.CloseLimitReq) (*entity.CloseLimitResp, error)
		RequestGetLimitInfo(ctx context.Context, request *entity.GetLimitInfoReq) (*entity.GetLimitInfoResp, error)
		RequestDeleteLimit(ctx context.Context, request *entity.DeleteLimitReq) (*entity.DeleteLimitResp, error)
	}

	ColvirProviderImpl struct {
		HTTPClient *http.Client
		BaseURL    string
		V2BaseURL  string
	}
)

func NewColvirProvider(cfg *Config) ColvirProvider {
	return &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    cfg.BaseURL,
		V2BaseURL:  cfg.V2BaseURL,
	}
}
