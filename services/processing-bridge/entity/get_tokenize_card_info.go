// package entity

// import (
// 	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
// )

// type (
// 	GetTokenizeCardInfoReq struct{}

// 	GetTokenizeCardInfoResult struct{}
// )

// // MakeGetTokenizeCardInfoPbToEntity создает объект из pb.GetTokenizeCardInfoReq в GetTokenizeCardInfoReq для передачи в usecase
// func MakeGetTokenizeCardInfoPbToEntity(req *pb.GetTokenizeCardInfoReq) *GetTokenizeCardInfoReq {
// 	if req == nil {
// 		return &GetTokenizeCardInfoReq{}
// 	}
// 	// write your mapping code here
// 	return &GetTokenizeCardInfoReq{}
// }

// // MakeGetTokenizeCardInfoEntityToPb создает объект из GetTokenizeCardInfo в pb.GetTokenizeCardInfoResp для возврата ответа из сервиса
// func MakeGetTokenizeCardInfoEntityToPb(res *GetTokenizeCardInfoResult) *pb.GetTokenizeCardInfoResp {
// 	return &pb.GetTokenizeCardInfoResp{}
// }

package entity

import (
	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	GetTokenizeCardInfoReq struct {
		CardID string
	}

	GetTokenizeCardInfoResult struct {
		IssuerCardRefID     string `json:"issuerCardRefId"`
		VirtualCardID       string `json:"virtualCardId"`
		WalletCardRefID     string `json:"walletCardRefId"`
		WalletProviderID    string `json:"walletProviderId"`
		WalletVirtualCardID string `json:"walletVirtualCardId"`
		Status              string `json:"status"`
	}
)

func MapSilkToCardInfoResult(silk *entity.GetCardInfoResponse) *GetTokenizeCardInfoResult {
	if silk == nil {
		return nil
	}
	return &GetTokenizeCardInfoResult{
		IssuerCardRefID:     silk.IssuerCardRefID,
		VirtualCardID:       silk.VirtualCardID,
		WalletCardRefID:     silk.WalletCardRefID,
		WalletProviderID:    silk.WalletProviderID,
		WalletVirtualCardID: silk.WalletVirtualCardID,
		Status:              silk.Status,
	}
}

// MakeGetTokenizeCardInfoPbToEntity создает объект из pb.GetTokenizeCardInfoReq в GetTokenizeCardInfoReq для передачи в usecase
func MakeGetTokenizeCardInfoPbToEntity(req *pb.GetTokenizeCardInfoReq) *GetTokenizeCardInfoReq {
	if req == nil {
		return &GetTokenizeCardInfoReq{}
	}
	// write your mapping code here
	return &GetTokenizeCardInfoReq{}
}

// MakeGetTokenizeCardInfoEntityToPb создает объект из GetCardInfo в pb.GetCardInfoResp для возврата ответа из сервиса
func MakeGetTokenizeCardInfoEntityToPb(res *GetTokenizeCardInfoResult) *pb.GetTokenizeCardInfoResp {
	if res == nil {
		return &pb.GetTokenizeCardInfoResp{}
	}

	return &pb.GetTokenizeCardInfoResp{
		IssuerCardRefId:     res.IssuerCardRefID,
		VirtualCardId:       res.VirtualCardID,
		WalletCardRefId:     res.WalletCardRefID,
		WalletProviderId:    res.WalletProviderID,
		WalletVirtualCardId: res.WalletVirtualCardID,
		Status:              pb.TokenStatus(pb.TokenStatus_value[res.Status]),
	}
}
