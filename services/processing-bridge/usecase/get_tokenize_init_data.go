package usecase

import (
	"context"
	"errors"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

// GetTokenizeInitData получает данные для инициализации токенизации карты через Silkpay
func (u *useCasesImpl) GetTokenizeInitData(ctx context.Context, req *entity.GetTokenizeInitDataReq) (*entity.GetTokenizeInitDataResult, error) {
	if req.Wallet == "" {
		return nil, errors.New("wallet required")
	}

	silk, err := u.Providers.SilkpayProvider.GetTokenizeInitData(ctx, req.CardID, req.Wallet)
	if err != nil {
		return nil, err
	}

	return &entity.GetTokenizeInitDataResult{
		PublicKeyID:      silk.PublicKeyID,
		OPC:              silk.OPC,
		EncryptedPayload: silk.EncryptedPayload,
		IssuerAppID:      silk.IssuerAppID,
		ProductID:        silk.ProductID,
	}, nil
}
