package usecase

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (u *useCasesImpl) GetTokenizeCardInfo(ctx context.Context, req *entity.GetTokenizeCardInfoReq) (*entity.GetTokenizeCardInfoResult, error) {
	customLogger := logs.FromContext(ctx).With().
		Str("request_id", utils.ExtractRequestID(ctx)).
		Str("issuer_card_ref_id", req.CardID).
		Logger()
	customLogger.Info().Msg("start – UC GetCardInfo (processing-bridge)")
	defer customLogger.Info().Msg("end – UC GetCardInfo (processing-bridge)")

	customLogger.Info().Msg("call SilkpayProvider.GetCardInfo")
	silkResp, err := u.Providers.SilkpayProvider.GetTokenizeCardInfo(ctx, req.CardID)
	customLogger.Info().Msg("response SilkpayProvider.GetCardInfo")
	if err != nil {
		customLogger.Error().Err(err).Msg("SilkpayProvider.GetCardInfo")
		return nil, err
	}

	result := entity.MapSilkToCardInfoResult(silkResp)

	return result, nil
}
