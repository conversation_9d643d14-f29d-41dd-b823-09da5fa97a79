package tests

import (
	"context"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

// TestGetTokenizeCardInfo_Success проверяет успешное получение информации о карте через Silkpay
func (s *Suite) TestGetTokenizeCardInfo_Success() {
	cardID := "fcf44eb0-d9bb-4ba8-9080-01730e1e3513" // такой же в golden files

	silkResp := &entity.GetCardInfoResponse{
		IssuerCardRefID:     cardID,
		VirtualCardID:       "vcard‑123",
		WalletCardRefID:     "wcr‑456",
		WalletProviderID:    "google_wallet",
		WalletVirtualCardID: "wvc‑789",
		Status:              "TOKENIZED",
	}

	s.mocks.Providers.SilkpayProvider.EXPECT().
		GetTokenizeCardInfo(gomock.Any(), cardID).
		Return(silkResp, nil).
		Times(1)

	resp, err := s.grpc.GetTokenizeCardInfo(context.Background(), &pb.GetTokenizeCardInfoReq{
		IssuerCardRefId: cardID,
	})

	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.CompareGolden(resp)
}
