package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
	dictEntity "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/entity"
	smecountrysync "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-countrysync"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
)

func (u *useCasesImpl) GetCountryList(ctx context.Context, req *entity.GetCountryListReq) (*entity.GetCountryListResult, error) {
	acceptLanguage, err := locale.FromContext(ctx, locale.Kk)
	if err != nil {
		return nil, err
	}

	if req.Filter != nil {
		codes := req.Filter.Codes
		if len(codes) > 0 {
			dictResp, err := u.Providers.Dictionary.DocGetListByFilter(ctx, &dictionary.DocGetListByFilterReq{
				DictId: smecountrysync.DictName,
				Filters: []*dictionary.Filter{
					{
						DataField: consts.DataCodeField,
						Operation: consts.DictOperationIn,
						Value:     strings.Join(codes, ","),
					},
				},
			})
			if err != nil {
				return nil, fmt.Errorf("failed to get sme-country list by filter: %w", err)
			}
			uint64()
			return dictListToCountryListResult(dictResp.GetList(), acceptLanguage)
		}
	}

	dictResp, err := u.Providers.Dictionary.DocGetListByName(ctx, &dictionary.DocGetListByNameReq{
		DictName: smecountrysync.DictName,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get sme-country list: %w", err)
	}

	return dictListToCountryListResult(dictResp.GetList(), acceptLanguage)
}

func dictListToCountryListResult(list []*dictionary.Doc, _ locale.Locale) (*entity.GetCountryListResult, error) {
	result := &entity.GetCountryListResult{
		Countries: make([]entity.Country, 0, len(list)),
	}
	for _, item := range list {
		if item == nil {
			continue
		}

		var country dictEntity.SmeCountry
		err := json.Unmarshal([]byte(item.GetData()), &country)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal sme-country data: %w", err)
		}

		result.Countries = append(result.Countries, entity.Country{
			Code:        country.Code,
			Name:        country.Name,
			Description: country.Description,
		})
	}

	return result, nil
}
