package consts

// Тип платежа в бюджет (ОПВ, СО, ОСМС и др.)
//
// Ссылка на описание: https://rmrkz.atlassian.net/wiki/spaces/ZS/pages/121077763/Payments+SME
type PaymentType string

const (
	// Обязательный пенсионный взнос
	PaymentTypeMandatoryPensionContribution PaymentType = "C"
	// Пенсионный перевод
	PaymentTypePensionTransfer PaymentType = "D"
	// Взносы в ФОМС (Social Health Insurance Fund)
	PaymentTypeSHIFContribution PaymentType = "M"
	// Отчисление на обязательное мед. страхование (ООСМС)
	PaymentTypeMSHIContribution PaymentType = "R"
	// Обязательный социальный взнос
	PaymentTypeMandatorySocialContribution PaymentType = "S"
	// Добровольный пенсионный взнос
	PaymentTypeVoluntaryPensionContribution PaymentType = "V"
	// Обязательные пенсионные взносы работодателя
	PaymentTypeEmployerMandatoryPensionContribution PaymentType = "E"
	// Обязательный профессиональный пенсионный взнос
	PaymentTypeMandatoryProfessionalPensionContribution PaymentType = "P"
	// не бюджетный платеж
	PaymentTypeNotBudgetPayment PaymentType = ""
)

func (pt PaymentType) String() string {
	return string(pt)
}

func (pt PaymentType) Values() []string {
	return []string{
		PaymentTypeMandatoryPensionContribution.String(),
		PaymentTypePensionTransfer.String(),
		PaymentTypeSHIFContribution.String(),
		PaymentTypeMSHIContribution.String(),
		PaymentTypeMandatorySocialContribution.String(),
		PaymentTypeVoluntaryPensionContribution.String(),
		PaymentTypeEmployerMandatoryPensionContribution.String(),
		PaymentTypeMandatoryProfessionalPensionContribution.String(),
	}
}

// Таблица решений W2_PMNT_PARAMS Colvir (тип документа в Колвире)
//
// Ссылка на описание типов: https://rmrkz.atlassian.net/wiki/spaces/ZS/pages/135430243
type DomesticMassPaymentDocumentType string

const (
	// Обязательный пенсионный взнос (ОПВ)
	DomesticMassPaymentDocumentTypePension DomesticMassPaymentDocumentType = "MASSPENM"
	// Обязательный пенсионные взнос работодателя
	DomesticMassPaymentDocumentTypeEmployerPension DomesticMassPaymentDocumentType = "MASSCOMP"
	// Социальные отчисления
	DomesticMassPaymentDocumentTypeSocial DomesticMassPaymentDocumentType = "MASSSOC"
	// Медицинское страхование
	DomesticMassPaymentDocumentTypeMedical DomesticMassPaymentDocumentType = "MASSMED"
	// Медицинское страхование работодателя
	DomesticMassPaymentDocumentTypeEmployerMedical DomesticMassPaymentDocumentType = "MASSMEDM"
	// Профессиональный пенсионный взнос
	DomesticMassPaymentDocumentTypeProfessionalPension DomesticMassPaymentDocumentType = "MASSPROF"
	// Налоговые платежи
	DomesticMassPaymentDocumentTypeTaxSocial DomesticMassPaymentDocumentType = "DPYTAX"
)

func (dmpdt DomesticMassPaymentDocumentType) String() string {
	return string(dmpdt)
}

func (dmpdt DomesticMassPaymentDocumentType) Values() []string {
	return []string{
		DomesticMassPaymentDocumentTypePension.String(),
		DomesticMassPaymentDocumentTypeEmployerPension.String(),
		DomesticMassPaymentDocumentTypeSocial.String(),
		DomesticMassPaymentDocumentTypeMedical.String(),
		DomesticMassPaymentDocumentTypeEmployerMedical.String(),
		DomesticMassPaymentDocumentTypeProfessionalPension.String(),
		DomesticMassPaymentDocumentTypeTaxSocial.String(),
	}
}

// Тип платежа в колвир (внутренний платёж, внутренний платёж с реестром)
type PaymentTypeColvir string

const (
	PaymentTypeColvirInternalPayment             PaymentTypeColvir = "Внутренний платёж"
	PaymentTypeColvirInternalPaymentWithRegistry PaymentTypeColvir = "Внутренний платёж с реестром"
)

func (pt PaymentTypeColvir) String() string {
	return string(pt)
}

func (pt PaymentTypeColvir) Values() []string {
	return []string{
		PaymentTypeColvirInternalPayment.String(),
		PaymentTypeColvirInternalPaymentWithRegistry.String(),
	}
}
