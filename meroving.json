{"project": {"module_name": "zaman", "package": "git.redmadrobot.com/zaman/backend/zaman", "golang_version": "1.23.1", "core_library_version": "1.11.35", "secure_error": true}, "gateways": [{"name": "mobile", "port": 8080, "base_url": "/api/v1", "openapi": "openapi.yaml", "gen_from_openapi": true, "timeout": "15000ms", "cache_ttl": "300s", "rate": {"max_rate": 10, "every": "1m"}, "cors": {"origins": ["*"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"], "credentials": true}, "security": {"token": "jwt"}, "providers": ["jaeger", "sentry", "vault"], "endpoints": [{"name": "health", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}, "backend": [{"service": "users", "method": "HealthCheck"}, {"service": "otp", "method": "HealthCheck"}, {"service": "documents", "method": "HealthCheck"}, {"service": "notifications", "method": "HealthCheck"}, {"service": "keycloak-proxy", "method": "HealthCheck"}, {"service": "kgd-bridge", "method": "HealthCheck"}, {"service": "bts-bridge", "method": "HealthCheck"}, {"service": "sms-bridge", "method": "HealthCheck"}, {"service": "loans", "method": "HealthCheck"}, {"service": "colvir-bridge", "method": "HealthCheck"}, {"service": "payments", "method": "HealthCheck"}, {"service": "cards-accounts", "method": "HealthCheck"}, {"service": "dictionary", "method": "HealthCheck"}, {"service": "pkb-bridge", "method": "HealthCheck"}, {"service": "task-manager", "method": "HealthCheck"}, {"service": "scoring", "method": "HealthCheck"}, {"service": "jira-bridge", "method": "HealthCheck"}, {"service": "deposits", "method": "HealthCheck"}, {"service": "bsas-bridge", "method": "HealthCheck"}, {"service": "referral", "method": "HealthCheck"}, {"service": "aml-bridge", "method": "HealthCheck"}, {"service": "seon-bridge", "method": "HealthCheck"}, {"service": "spr-bridge", "method": "HealthCheck"}, {"service": "qazpost-bridge", "method": "HealthCheck"}, {"service": "liveness", "method": "HealthCheck"}, {"service": "juicyscore-bridge", "method": "HealthCheck"}, {"service": "file-guard", "method": "HealthCheck"}, {"service": "collection", "method": "HealthCheck"}, {"service": "ap-bridge", "method": "HealthCheck"}, {"service": "alt-score-bridge", "method": "HealthCheck"}, {"service": "antifraud", "method": "HealthCheck"}, {"service": "processing-bridge", "method": "HealthCheck"}, {"service": "payments-sme", "method": "HealthCheck"}, {"service": "balance-updater", "method": "HealthCheck"}, {"service": "bitrix-bridge", "method": "HealthCheck"}, {"service": "crm", "method": "HealthCheck"}, {"service": "kaspi-bridge", "method": "HealthCheck"}, {"service": "tokenize", "method": "HealthCheck"}]}, {"name": "otpRetry", "backend": [{"service": "otp", "method": "GenerateRetryCode", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}}]}, {"name": "auth<PERSON><PERSON><PERSON>", "backend": [{"service": "users", "method": "<PERSON><PERSON>"}]}, {"name": "authConfirm", "backend": [{"service": "users", "method": "ConfirmL<PERSON>in"}]}, {"name": "authRefresh", "backend": [{"service": "users", "method": "RefreshToken"}]}, {"name": "authLogout", "backend": [{"service": "users", "method": "Logout"}]}, {"name": "profileDelete", "backend": [{"service": "users", "method": "Logout"}]}, {"name": "createAccountsDocument", "backend": [{"service": "documents", "method": "AccountDocument"}]}, {"name": "getDocumentByID", "backend": [{"service": "documents", "method": "GetDocumentByID"}]}, {"name": "requestDocumentPublic", "backend": [{"service": "documents", "method": "GenerateDocumentByType"}]}, {"name": "authIdentify", "backend": [{"service": "users", "method": "Identify"}]}, {"name": "documentForSign", "backend": [{"service": "users", "method": "GetDocumentForSign"}]}, {"name": "signDocumentByID", "backend": [{"service": "documents", "method": "SignDocumentByID"}]}, {"name": "confirmSignDocumentByID", "backend": [{"service": "documents", "method": "ConfirmSignDocument"}]}, {"name": "confirmSignDocuments", "backend": [{"service": "documents", "method": "ConfirmSignDocuments"}]}, {"name": "signDocumentByIDs", "backend": [{"service": "documents", "method": "SignDocumentByID"}]}, {"name": "confirmSignDocumentsBatch", "backend": [{"service": "documents", "method": "ConfirmSignDocumentsBatch"}]}, {"name": "loansConfirmSignDocumentByID", "backend": [{"service": "loans", "method": "ConfirmSignDocument"}]}, {"name": "loansConfirmSignDocuments", "backend": [{"service": "loans", "method": "ConfirmSignDocument"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "backend": [{"service": "loans", "method": "Get<PERSON><PERSON><PERSON>"}]}, {"name": "getLoansOnboardingTexts", "backend": [{"service": "loans", "method": "GetCreditOnboardingTexts"}]}, {"name": "getLoansCalcData", "backend": [{"service": "loans", "method": "GetLoanCalcData"}]}, {"name": "loanDocumentForSign", "backend": [{"service": "loans", "method": "GetDocumentForSign"}]}, {"name": "calculateLoanTerms", "backend": [{"service": "loans", "method": "CalculateLoanTerms"}]}, {"name": "userCards", "backend": [{"service": "cards-accounts", "method": "GetCards"}]}, {"name": "getUserAccounts", "backend": [{"service": "cards-accounts", "method": "GetAccount"}]}, {"name": "getLoanSurvey", "backend": [{"service": "loans", "method": "GetSurvey"}]}, {"name": "saveLoan<PERSON>urvey", "backend": [{"service": "loans", "method": "Save<PERSON>urvey"}]}, {"name": "createLoanApplication", "backend": [{"service": "loans", "method": "CreateLoanApplication"}]}, {"name": "dictCreate", "backend": [{"service": "dictionary", "method": "DictCreate"}]}, {"name": "dictUpdate", "backend": [{"service": "dictionary", "method": "DictUpdate"}]}, {"name": "dictDelete", "backend": [{"service": "dictionary", "method": "DictDelete"}]}, {"name": "dictGetList", "backend": [{"service": "dictionary", "method": "DictGetList"}]}, {"name": "dictGet", "backend": [{"service": "dictionary", "method": "DictGet"}]}, {"name": "dictDocCreate", "backend": [{"service": "dictionary", "method": "DocCreate"}]}, {"name": "dictDocUpdate", "backend": [{"service": "dictionary", "method": "DocUpdate"}]}, {"name": "dictDocDelete", "backend": [{"service": "dictionary", "method": "DocDelete"}]}, {"name": "dictDocGet", "backend": [{"service": "dictionary", "method": "DocGet"}]}, {"name": "dictDocGetList", "backend": [{"service": "dictionary", "method": "DocGetList"}]}, {"name": "dictDocGetByName", "backend": [{"service": "dictionary", "method": "DocGetByName"}]}, {"name": "dictDocGetListByName", "backend": [{"service": "dictionary", "method": "DocGetListByName"}]}, {"name": "dict<PERSON>ob<PERSON>un", "backend": [{"service": "dictionary", "method": "JobRun"}]}, {"name": "loansGetInternalChecksResult", "backend": [{"service": "loans", "method": "GetInternalChecksResult"}]}, {"name": "dictJobStop", "backend": [{"service": "dictionary", "method": "JobStop"}]}, {"name": "loansGetEducationTypes", "backend": [{"service": "loans", "method": "GetEducationTypes"}]}, {"name": "dictJobGetStatus", "backend": [{"service": "dictionary", "method": "JobGetStatus"}]}, {"name": "dictJobGetStatusAll", "backend": [{"service": "dictionary", "method": "JobGetStatusAll"}]}, {"name": "dictDocGetListByFilters", "backend": [{"service": "dictionary", "method": "DocGetListByFilter"}]}, {"name": "dictDocGetTreeLine", "backend": [{"service": "dictionary", "method": "DocTreeGetLine"}]}, {"name": "dictKATOMapFromTSOID", "backend": [{"service": "dictionary", "method": "KATOMapFromTSOID"}]}, {"name": "dictDocOrderUpdate", "backend": [{"service": "dictionary", "method": "DocOrderUpdate"}]}, {"name": "loansGetEmploymentTypes", "backend": [{"service": "loans", "method": "GetEmploymentTypes"}]}, {"name": "loansGetRelationTypes", "backend": [{"service": "loans", "method": "GetRelationTypes"}]}, {"name": "getTransactions", "backend": [{"service": "payments", "method": "GetTransactions"}]}, {"name": "paymentsCheckAccountIin", "backend": [{"service": "payments", "method": "CheckAccountIin"}]}, {"name": "loansCancelLoanApplication", "backend": [{"service": "loans", "method": "CancelLoanApplication"}]}, {"name": "documentsForLoanApp", "backend": [{"service": "loans", "method": "GenerateLoanAppDocuments"}]}, {"name": "getPaymentHistory", "backend": [{"service": "payments", "method": "GetTransactions"}]}, {"name": "getTransactionByID", "backend": [{"service": "payments", "method": "GetTransactionByID"}]}, {"name": "getTransactionReceipt", "backend": [{"service": "payments", "method": "GetTransactionReceipt"}]}, {"name": "loansCheckActiveLoanAppExists", "backend": [{"service": "loans", "method": "CheckActiveLoanAppExists"}]}, {"name": "createPaymentByAccount", "backend": [{"service": "payments", "method": "CreatePaymentByAccount"}]}, {"name": "confirmPaymentByAccount", "backend": [{"service": "payments", "method": "ConfirmPaymentByAccount"}]}, {"name": "getDictionaries", "backend": [{"service": "dictionary", "method": "DocGetListByFilter"}]}, {"name": "dictGetLocations", "backend": [{"service": "dictionary", "method": "DocGetListByFilter"}]}, {"name": "getDictionariesByFilter", "backend": [{"service": "dictionary", "method": "DocGetListByFilter"}]}, {"name": "loansGetApprovedLoanAppStatus", "backend": [{"service": "loans", "method": "GetApprovedLoanAppStatus"}]}, {"name": "publishLoanAppData", "backend": [{"service": "loans", "method": "PublishSprLoanData"}]}, {"name": "getScoringResult", "backend": [{"service": "loans", "method": "GetScoringResult"}]}, {"name": "getBtsDataForLoanApp", "backend": [{"service": "loans", "method": "GetBtsData"}]}, {"name": "usersUpdateUserLocale", "backend": [{"service": "users", "method": "UpdateUserLocale"}]}, {"name": "getBtsDataForAuth", "backend": [{"service": "users", "method": "GetLivenessLink"}]}, {"name": "loansPostIdentifyBtsData", "backend": [{"service": "loans", "method": "PostIdentifyBtsData"}]}, {"name": "loansPostEdsBtsData", "backend": [{"service": "loans", "method": "PostEdsBtsData"}]}, {"name": "getBankStatement", "backend": [{"service": "loans", "method": "GetBanksForStatement"}]}, {"name": "getBankStatementV2", "backend": [{"service": "loans", "method": "GetBanksForStatementV2"}]}, {"name": "getLoansDetails", "backend": [{"service": "loans", "method": "GetLoansDetails"}]}, {"name": "getTaskByID", "backend": [{"service": "task-manager", "method": "GetTaskInfo"}]}, {"name": "getTasks", "backend": [{"service": "task-manager", "method": "GetTasksList"}]}, {"name": "postUnsecurePdfFile", "backend": [{"service": "file-guard", "method": "UploadFile"}]}, {"name": "getProductInfo", "backend": [{"service": "deposits", "method": "GetProductInfo"}]}, {"name": "depositConditionProfit", "backend": [{"service": "deposits", "method": "DepositConditionProfit"}]}, {"name": "getAvailableAccounts", "backend": [{"service": "deposits", "method": "GetAvailableAccounts"}]}, {"name": "createDepositOffer", "backend": [{"service": "deposits", "method": "CreateDepositOffer"}]}, {"name": "getUserDeposits", "backend": [{"service": "deposits", "method": "GetUserDeposits"}]}, {"name": "getDepositDetail", "backend": [{"service": "deposits", "method": "GetDepositDetail"}]}, {"name": "checkPhoneNumber", "backend": [{"service": "payments", "method": "CheckPhoneNumber"}]}, {"name": "createPaymentForMobile", "backend": [{"service": "payments", "method": "CreatePaymentForMobile"}]}, {"name": "confirmPaymentForMobile", "backend": [{"service": "payments", "method": "ConfirmPaymentForMobile"}]}, {"name": "checkClientByPhoneNumber", "backend": [{"service": "payments", "method": "CheckClientByPhoneNumber"}]}, {"name": "createInternalPaymentByPhoneNumber", "backend": [{"service": "payments", "method": "CreateInternalPaymentByPhoneNumber"}]}, {"name": "confirmInternalPaymentByPhoneNumber", "backend": [{"service": "payments", "method": "ConfirmInternalPaymentByPhoneNumber"}]}, {"name": "getReferralProgramStatus", "backend": [{"service": "referral", "method": "CheckStatus"}]}, {"name": "getRefinancingInfo", "backend": [{"service": "loans", "method": "GetRefinancingInfo"}]}, {"name": "saveUserExternalBankLoans", "backend": [{"service": "loans", "method": "SaveUserExternalBankLoans"}]}, {"name": "loansPostEarlyRepay", "backend": [{"service": "loans", "method": "PostEarlyRepay"}]}, {"name": "createSelfTransfer", "backend": [{"service": "payments", "method": "CreateSelfTransfer"}]}, {"name": "referralProgramStatus", "backend": [{"service": "referral", "method": "ReferralProgramStatus"}]}, {"name": "referralProgramProfile", "backend": [{"service": "referral", "method": "ReferralProgramProfile"}]}, {"name": "referralProgramOnboarding", "backend": [{"service": "referral", "method": "ReferralProgramOnboarding"}]}, {"name": "referralProgramAttribution", "backend": [{"service": "referral", "method": "ReferralProgramAttribution"}]}, {"name": "applicationForSign", "backend": [{"service": "cards-accounts", "method": "GetApplicationForSign"}]}]}, {"name": "sme", "port": 8081, "base_url": "/api/v1", "openapi": "openapi.yaml", "gen_from_openapi": true, "timeout": "15000ms", "cache_ttl": "300s", "rate": {"max_rate": 10, "every": "1m"}, "cors": {"origins": ["*"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"], "credentials": true}, "security": {"token": "jwt"}, "providers": ["vault", "jaeger", "sentry", "vault"], "endpoints": [{"name": "health", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}, "backend": [{"service": "users", "method": "HealthCheck"}, {"service": "otp", "method": "HealthCheck"}, {"service": "documents", "method": "HealthCheck"}, {"service": "notifications", "method": "HealthCheck"}, {"service": "keycloak-proxy", "method": "HealthCheck"}, {"service": "kgd-bridge", "method": "HealthCheck"}, {"service": "bts-bridge", "method": "HealthCheck"}, {"service": "sms-bridge", "method": "HealthCheck"}, {"service": "loans", "method": "HealthCheck"}, {"service": "colvir-bridge", "method": "HealthCheck"}, {"service": "payments", "method": "HealthCheck"}, {"service": "cards-accounts", "method": "HealthCheck"}, {"service": "dictionary", "method": "HealthCheck"}, {"service": "pkb-bridge", "method": "HealthCheck"}, {"service": "task-manager", "method": "HealthCheck"}, {"service": "scoring", "method": "HealthCheck"}, {"service": "jira-bridge", "method": "HealthCheck"}, {"service": "deposits", "method": "HealthCheck"}, {"service": "bsas-bridge", "method": "HealthCheck"}, {"service": "referral", "method": "HealthCheck"}, {"service": "aml-bridge", "method": "HealthCheck"}, {"service": "seon-bridge", "method": "HealthCheck"}, {"service": "spr-bridge", "method": "HealthCheck"}, {"service": "qazpost-bridge", "method": "HealthCheck"}, {"service": "liveness", "method": "HealthCheck"}, {"service": "juicyscore-bridge", "method": "HealthCheck"}, {"service": "file-guard", "method": "HealthCheck"}, {"service": "collection", "method": "HealthCheck"}, {"service": "ap-bridge", "method": "HealthCheck"}, {"service": "alt-score-bridge", "method": "HealthCheck"}, {"service": "antifraud", "method": "HealthCheck"}, {"service": "processing-bridge", "method": "HealthCheck"}, {"service": "payments-sme", "method": "HealthCheck"}, {"service": "balance-updater", "method": "HealthCheck"}, {"service": "bitrix-bridge", "method": "HealthCheck"}, {"service": "crm", "method": "HealthCheck"}, {"service": "kaspi-bridge", "method": "HealthCheck"}, {"service": "tokenize", "method": "HealthCheck"}]}, {"name": "otpRetry", "backend": [{"service": "otp", "method": "GenerateRetryCode", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}}]}, {"name": "auth<PERSON><PERSON><PERSON>", "backend": [{"service": "users", "method": "<PERSON><PERSON>"}]}, {"name": "authConfirm", "backend": [{"service": "users", "method": "ConfirmL<PERSON>in"}]}, {"name": "authRefresh", "backend": [{"service": "users", "method": "RefreshToken"}]}, {"name": "authLogout", "backend": [{"service": "users", "method": "Logout"}]}, {"name": "profileDelete", "backend": [{"service": "users", "method": "Logout"}]}, {"name": "getDocumentByID", "backend": [{"service": "documents", "method": "GetDocumentByID"}]}, {"name": "requestDocumentPublic", "backend": [{"service": "documents", "method": "GenerateDocumentByType"}]}, {"name": "authIdentify", "backend": [{"service": "users", "method": "Identify"}]}, {"name": "signDocumentByID", "backend": [{"service": "documents", "method": "SignDocumentByID"}]}, {"name": "confirmSignDocumentByID", "backend": [{"service": "documents", "method": "ConfirmSignDocument"}]}, {"name": "confirmSignDocumentsBatch", "backend": [{"service": "documents", "method": "ConfirmSignDocumentsBatch"}]}, {"name": "loansClientApplicationCheck", "backend": [{"service": "loans", "method": "CheckActiveLoanAppExists"}]}, {"name": "getLoansCalcData", "backend": [{"service": "loans", "method": "GetLoanCalcDataSme"}]}, {"name": "getLoansOnboardingTexts", "backend": [{"service": "loans", "method": "GetCreditOnboardingTexts"}]}, {"name": "loansGetInternalChecksResult", "backend": [{"service": "loans", "method": "GetInternalChecksResult"}]}, {"name": "loansGetApprovedLoanAppStatus", "backend": [{"service": "loans", "method": "GetApprovedLoanAppStatus"}]}, {"name": "createLoanApplication", "backend": [{"service": "loans", "method": "CreateLoanApplication"}]}, {"name": "getTransactions", "backend": [{"service": "payments", "method": "GetTransactions"}]}, {"name": "paymentsCheckAccountIin", "backend": [{"service": "payments", "method": "CheckAccountIin"}]}, {"name": "getPaymentHistory", "backend": [{"service": "payments", "method": "GetTransactions"}]}, {"name": "getTransactionByID", "backend": [{"service": "payments", "method": "GetTransactionByID"}]}, {"name": "getTransactionReceipt", "backend": [{"service": "payments", "method": "GetTransactionReceipt"}]}, {"name": "createPaymentByAccount", "backend": [{"service": "payments", "method": "CreatePaymentByAccount"}]}, {"name": "confirmPaymentByAccount", "backend": [{"service": "payments", "method": "ConfirmPaymentByAccount"}]}, {"name": "smePaymentsClient", "backend": [{"service": "payments-sme", "method": "SmePaymentsClient"}]}, {"name": "confirmPaymentSme", "backend": [{"service": "payments-sme", "method": "ConfirmPaymentSme"}]}, {"name": "smePaymentsCreateOtp", "backend": [{"service": "payments-sme", "method": "SmePaymentsCreateOtp"}]}, {"name": "smePaymentsOtpResend", "backend": [{"service": "payments-sme", "method": "SmePaymentsOtpResend"}]}, {"name": "smePaymentsOtpValidate", "backend": [{"service": "payments-sme", "method": "SmePaymentsOtpValidate"}]}, {"name": "smePaymentsWorktime", "backend": [{"service": "payments-sme", "method": "SmePaymentsWorktime"}]}, {"name": "smePaymentsGetPaymentOrder", "backend": [{"service": "payments-sme", "method": "SmePaymentsGetPaymentOrder"}]}, {"name": "smePaymentsGetPaymentOrderByTrNumber", "backend": [{"service": "payments-sme", "method": "SmePaymentsGetPaymentOrderByTrNumber"}]}, {"name": "smePaymentsGetEmployeeList", "backend": [{"service": "payments-sme", "method": "GetEmployeeList"}]}, {"name": "smePaymentsDeleteEmployeeByID", "backend": [{"service": "payments-sme", "method": "DeleteEmployee"}]}, {"name": "smePaymentsCreateEmployee", "backend": [{"service": "payments-sme", "method": "CreateEmployee"}]}, {"name": "smePaymentsUpdateEmployeeByID", "backend": [{"service": "payments-sme", "method": "UpdateEmployee"}]}, {"name": "dictDocGetListByFilters", "backend": [{"service": "dictionary", "method": "DocGetListByFilter"}]}, {"name": "confirmSignDocuments", "backend": [{"service": "documents", "method": "ConfirmSignDocuments"}]}, {"name": "signDocumentByIDs", "backend": [{"service": "documents", "method": "SignDocumentByID"}]}, {"name": "getBankStatement", "backend": [{"service": "loans", "method": "GetBanksForStatement"}]}, {"name": "getBankStatementV2", "backend": [{"service": "loans", "method": "GetBanksForStatementV2"}]}, {"name": "userCards", "backend": [{"service": "cards-accounts", "method": "GetCards"}]}, {"name": "getTaskByID", "backend": [{"service": "task-manager", "method": "GetTaskInfo"}]}, {"name": "getTasks", "backend": [{"service": "task-manager", "method": "GetTasksList"}]}, {"name": "getBtsDataForAuth", "backend": [{"service": "users", "method": "GetLivenessLink"}]}, {"name": "postUnsecurePdfFile", "backend": [{"service": "file-guard", "method": "UploadFile"}]}, {"name": "getBtsDataForLoanApp", "backend": [{"service": "loans", "method": "GetBtsData"}]}, {"name": "loansPostIdentifyBtsDataSme", "backend": [{"service": "loans", "method": "PostIdentifyBtsDataSme"}]}, {"name": "loansPostEdsBtsData", "backend": [{"service": "loans", "method": "PostEdsBtsData"}]}, {"name": "saveLoan<PERSON>urvey", "backend": [{"service": "loans", "method": "Save<PERSON>urvey"}]}, {"name": "getLoanSurvey", "backend": [{"service": "loans", "method": "GetSurvey"}]}, {"name": "dictGetLocations", "backend": [{"service": "dictionary", "method": "DocGetListByFilter"}]}, {"name": "loansConfirmSignDocumentByID", "backend": [{"service": "loans", "method": "ConfirmSignDocument"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "backend": [{"service": "loans", "method": "Get<PERSON><PERSON><PERSON>"}]}, {"name": "getLoansDetails", "backend": [{"service": "loans", "method": "GetLoansDetails"}]}, {"name": "documentForSign", "backend": [{"service": "users", "method": "GetDocumentForSign"}]}, {"name": "calculateLoanTerms", "backend": [{"service": "loans", "method": "CalculateLoanTerms"}]}, {"name": "documentsForLoanApp", "backend": [{"service": "loans", "method": "GenerateLoanAppDocuments"}]}, {"name": "usersUpdateUserLocale", "backend": [{"service": "users", "method": "UpdateUserLocale"}]}, {"name": "createAccountsDocument", "backend": [{"service": "documents", "method": "AccountDocument"}]}, {"name": "getUserAccounts", "backend": [{"service": "cards-accounts", "method": "GetAccount"}]}, {"name": "loansGetEducationTypes", "backend": [{"service": "loans", "method": "GetEducationTypes"}]}, {"name": "loansGetEmploymentTypes", "backend": [{"service": "loans", "method": "GetEmploymentTypes"}]}, {"name": "loansGetRelationTypes", "backend": [{"service": "loans", "method": "GetRelationTypes"}]}, {"name": "publishLoanAppData", "backend": [{"service": "loans", "method": "PublishSprLoanData"}]}, {"name": "getScoringResult", "backend": [{"service": "loans", "method": "GetScoringResult"}]}, {"name": "loansCancelLoanApplication", "backend": [{"service": "loans", "method": "CancelLoanApplication"}]}, {"name": "createOrderBsas", "backend": [{"service": "documents", "method": "CreateOrderBsas"}]}, {"name": "getOrderBsas", "backend": [{"service": "documents", "method": "GetOrderResultBsas"}]}, {"name": "getOtcBsas", "backend": [{"service": "documents", "method": "GetOtcReportInfo"}]}, {"name": "getSystemBsas", "backend": [{"service": "documents", "method": "GetSystemMapping"}]}, {"name": "getSellBsas", "backend": [{"service": "documents", "method": "GetSellToBMIS"}]}, {"name": "loansPostEarlyRepay", "backend": [{"service": "loans", "method": "PostEarlyRepay"}]}, {"name": "changeDisbursementControl", "backend": [{"service": "loans", "method": "ChangeDisbursementControl"}]}, {"name": "getUserAccountsSME", "backend": [{"service": "cards-accounts", "method": "GetAccountsSME"}]}, {"name": "getKbeKodList", "backend": [{"service": "payments-sme", "method": "GetKbeKodList"}]}, {"name": "getKnpList", "backend": [{"service": "payments-sme", "method": "GetKnpList"}]}, {"name": "getBankList", "backend": [{"service": "payments-sme", "method": "GetBankList"}]}, {"name": "getKbkList", "backend": [{"service": "payments-sme", "method": "GetKbkList"}]}, {"name": "getTaxAuthorityList", "backend": [{"service": "payments-sme", "method": "GetTaxAuthorityList"}]}, {"name": "createPayment", "backend": [{"service": "payments-sme", "method": "CreatePayment"}]}, {"name": "currencyCheck", "backend": [{"service": "cards-accounts", "method": "GetAvailableAccountsByCurrencySME"}]}, {"name": "clientVerification", "backend": [{"service": "cards-accounts", "method": "VerifyClient"}]}, {"name": "documentsForSign", "backend": [{"service": "cards-accounts", "method": "GetDocumentsForSign"}]}]}, {"name": "paymentsgw", "port": 9197, "base_url": "/api/v1", "openapi": "openapi.yaml", "gen_from_openapi": true, "timeout": "30000ms", "cache_ttl": "300s", "rate": {"max_rate": 10, "every": "1m"}, "cors": {"origins": ["*"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"], "credentials": true}, "providers": ["jaeger", "sentry", "vault"], "endpoints": [{"name": "health", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}, "backend": [{"service": "payments", "method": "HealthCheck"}]}, {"name": "AstanaPlat", "backend": [{"service": "payments", "method": "AstanaPlat"}]}]}, {"name": "balancegw", "port": 8082, "base_url": "/api/v1", "openapi": "openapi.yaml", "gen_from_openapi": true, "timeout": "15000ms", "cache_ttl": "300s", "rate": {"max_rate": 10, "every": "1m"}, "cors": {"origins": ["*"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"], "credentials": true}, "providers": ["jaeger", "sentry", "vault"], "endpoints": [{"name": "health", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}, "backend": [{"service": "users", "method": "HealthCheck"}]}, {"name": "getBalance", "backend": [{"service": "cards-accounts", "method": "GetBalance"}]}, {"name": "healthBalanceUpdater", "backend": [{"service": "balance-updater", "method": "HealthCheck"}]}]}, {"name": "documentsgw", "port": 8080, "base_url": "/documents", "openapi": "openapi.yaml", "gen_from_openapi": false, "timeout": "15000ms", "cache_ttl": "300s", "rate": {"max_rate": 10, "every": "1m"}, "cors": {"origins": ["*"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"], "credentials": true}, "security": {"token": "jwt"}, "providers": ["jaeger", "sentry", "vault"], "endpoints": [{"name": "health", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}, "backend": [{"service": "documents", "method": "HealthCheck"}]}, {"name": "downloadPublicDoc", "backend": [{"service": "documents", "method": "DownloadDocument"}]}, {"name": "downloadPrivateDoc", "backend": [{"service": "documents", "method": "DownloadDocument"}]}]}, {"name": "crmgw", "port": 8085, "base_url": "/api/v1", "openapi": "openapi.yaml", "gen_from_openapi": true, "timeout": "15000ms", "cache_ttl": "300s", "rate": {"max_rate": 10, "every": "1m"}, "cors": {"origins": ["*"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"], "credentials": true, "security": {"token": "api-key"}}, "providers": ["jaeger", "vault", "sentry"], "endpoints": [{"name": "health", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}, "backend": [{"service": "crm", "method": "HealthCheck"}]}, {"name": "logoutUser", "backend": [{"service": "crm", "method": "LogoutUserByPhoneNumber"}]}, {"name": "getClientInfo", "backend": [{"service": "crm", "method": "GetClientInfo"}]}, {"name": "getLoanDetails", "backend": [{"service": "crm", "method": "GetLoanDetails"}]}, {"name": "getAccountDetails", "backend": [{"service": "crm", "method": "GetAccountDetails"}]}, {"name": "getAccountTransactions", "backend": [{"service": "crm", "method": "GetAccountTransactions"}]}, {"name": "getDepositDetails", "backend": [{"service": "crm", "method": "GetDepositDetails"}]}]}, {"name": "collectiongw", "port": 8086, "base_url": "/api/v1", "openapi": "openapi.yaml", "gen_from_openapi": true, "timeout": "15000ms", "cache_ttl": "300s", "rate": {"max_rate": 10, "every": "1m"}, "cors": {"origins": ["*"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"], "credentials": true}, "security": {"token": "jwt"}, "providers": ["jaeger", "sentry", "vault"], "endpoints": [{"name": "health", "extra_config": {"rate": {"max_rate": 10, "every": "1s"}}, "backend": [{"service": "collection", "method": "HealthCheck"}]}, {"name": "GetClientFileIncome", "backend": [{"service": "collection", "method": "GetClientFileIncome"}]}]}, {"name": "landing", "port": 8090, "base_url": "/api/v1", "openapi": "openapi.yaml", "gen_from_openapi": true, "timeout": "15000ms", "cache_ttl": "300s", "rate": {"max_rate": 10, "every": "1m"}, "cors": {"origins": ["*"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"], "credentials": true, "security": {}}, "providers": ["jaeger", "sentry", "vault"], "endpoints": [{"name": "signDocumentByIDWeb", "backend": [{"service": "documents", "method": "SignDocumentByIDWeb"}]}, {"name": "confirmSignDocumentByIDWeb", "backend": [{"service": "documents", "method": "ConfirmSignDocumentWeb"}]}]}], "services": [{"name": "users", "type": "logic", "description": "Users service", "dependencies": ["vault", "postgresql", "kafka", "redis", "s3", "grafana", "prometheus", "jaeger", "sentry", "minio"], "clients": {"grpc": ["documents", "notifications", "otp", "keycloak-proxy", "kgd-bridge", "bts-bridge", "colvir-bridge", "pkb-bridge", "dictionary", "liveness", "aml-bridge", "task-manager", "cards-accounts", "qazpost-bridge", "antifraud"]}}, {"name": "otp", "type": "logic", "description": "OTP service", "dependencies": ["vault", "kafka", "redis", "grafana", "prometheus", "jaeger", "sentry"], "clients": {"grpc": []}}, {"name": "documents", "type": "logic", "description": "Documents service", "dependencies": ["vault", "postgresql", "kafka", "s3", "grafana", "prometheus", "jaeger", "sentry", "minio", "<PERSON><PERSON>"], "clients": {"grpc": ["notifications", "otp", "users", "colvir-bridge", "payments", "task-manager", "cards-accounts", "pkb-bridge", "bsas-bridge", "dictionary"]}}, {"name": "notifications", "type": "logic", "description": "Notifications service", "dependencies": ["vault", "postgresql", "kafka", "redis", "s3", "grafana", "prometheus", "jaeger", "sentry"], "clients": {"grpc": ["sms-bridge", "users"]}}, {"name": "keycloak", "type": "proxy", "description": "Proxy for Keycloak API", "dependencies": ["vault", "grafana", "prometheus", "jaeger", "sentry", "keycloak"], "clients": {"grpc": []}}, {"name": "kgd", "type": "bridge", "mocks": ["kgd"], "description": "Service for KGD integration", "dependencies": ["vault", "postgresql", "mongodb", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "bts", "type": "bridge", "description": "Service for BTS integration", "dependencies": ["vault", "postgresql", "mongodb", "grafana", "prometheus", "jaeger", "sentry", "kafka", "bts"], "clients": {"grpc": ["documents"]}}, {"name": "sms", "type": "bridge", "description": "Service for SMS integration", "dependencies": ["vault", "mongodb", "grafana", "prometheus", "jaeger", "sentry", "postgresql"], "clients": {"grpc": []}}, {"name": "loans", "type": "logic", "description": "Loans service", "dependencies": ["vault", "grafana", "prometheus", "jaeger", "sentry", "postgresql", "chronos", "kafka"], "clients": {"grpc": ["users", "documents", "cards-accounts", "kgd-bridge", "colvir-bridge", "aml-bridge", "antifraud", "pkb-bridge", "bts-bridge", "dictionary", "liveness", "task-manager", "jira-bridge", "bsas-bridge", "payments", "processing-bridge", "file-guard"]}}, {"name": "colvir", "type": "bridge", "description": "Service for Colvir integration", "dependencies": ["vault", "postgresql", "mongodb", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "payments", "type": "logic", "description": "Payments service", "dependencies": ["vault", "grafana", "prometheus", "jaeger", "sentry", "postgresql", "kafka"], "clients": {"grpc": ["users", "colvir-bridge", "otp", "dictionary", "aml-bridge", "documents", "ap-bridge", "antifraud", "processing-bridge", "payments-sme"]}}, {"name": "cards-accounts", "type": "logic", "description": "Cards and accounts service", "dependencies": ["vault", "grafana", "prometheus", "jaeger", "sentry", "postgresql", "kafka", "chronos"], "clients": {"grpc": ["users", "colvir-bridge", "task-manager", "pkb-bridge", "dictionary", "processing-bridge", "notifications", "aml-bridge", "documents", "antifraud"]}}, {"name": "dictionary", "type": "logic", "description": "Dictionary service", "dependencies": ["vault", "s3", "redis", "grafana", "prometheus", "jaeger", "sentry", "postgresql"], "clients": {"grpc": ["colvir-bridge", "qazpost-bridge", "users", "bts-bridge", "dictionary"]}}, {"name": "pkb", "type": "bridge", "description": "Service for PKB integration", "dependencies": ["vault", "postgresql", "mongodb", "grafana", "prometheus", "jaeger", "sentry", "kafka", "pkb", "s3"], "clients": {"grpc": []}}, {"name": "aml", "type": "bridge", "description": "Service for AML integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka", "aml"], "clients": {"grpc": []}}, {"name": "liveness", "type": "logic", "description": "Liveness service", "dependencies": ["vault", "grafana", "prometheus", "jaeger", "sentry", "postgresql", "kafka", "chronos"], "clients": {"grpc": ["bts-bridge", "documents", "users"]}}, {"name": "task-manager", "type": "logic", "description": "Task manager service", "dependencies": ["vault", "postgresql", "kafka", "grafana", "prometheus", "jaeger", "sentry"], "clients": {"grpc": []}}, {"name": "juicyscore", "type": "bridge", "description": "Service for JuicyScore integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "jira", "type": "bridge", "description": "Service for Jira integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "file-guard", "type": "logic", "description": "FileGuard service", "dependencies": ["vault", "grafana", "prometheus", "jaeger", "sentry", "postgresql", "kafka", "s3"], "clients": {"grpc": []}}, {"name": "scoring", "type": "logic", "description": "Scoring service", "dependencies": ["vault", "grafana", "prometheus", "jaeger", "sentry", "postgresql", "kafka", "s3"], "clients": {"grpc": ["pkb-bridge", "alt-score-bridge", "juicyscore-bridge", "seon-bridge", "spr-bridge", "users", "bts-bridge", "file-guard", "dictionary"]}}, {"name": "seon", "type": "bridge", "description": "Service for Seon integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "spr", "type": "bridge", "description": "Service for spr integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "alt-score", "type": "bridge", "description": "Service for AltScore integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "qazpost", "type": "bridge", "description": "Service for Kazpost integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "deposits", "type": "logic", "description": "Logic deposits service", "dependencies": ["vault", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": ["cards-accounts", "documents", "users", "pkb-bridge", "colvir-bridge", "processing-bridge"]}}, {"name": "bsas", "type": "bridge", "description": "Service for BSAS integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "ap", "type": "bridge", "description": "Service for Astana plat integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "processing", "type": "bridge", "description": "Service for processing integrations", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "collection", "description": "overdue collection service", "type": "logic", "dependencies": ["vault", "postgresql", "kafka", "s3", "grafana", "prometheus", "jaeger", "sentry", "minio", "chronos"], "clients": {"grpc": ["colvir-bridge", "pkb-bridge", "users", "loans", "bts-bridge", "documents"]}}, {"name": "payments-sme", "type": "logic", "description": "SME payments service", "dependencies": ["vault", "grafana", "prometheus", "jaeger", "sentry", "postgresql", "kafka"], "clients": {"grpc": ["otp", "dictionary", "colvir-bridge", "users", "pkb-bridge", "documents"]}}, {"name": "referral", "type": "logic", "description": "Service for referral program", "dependencies": ["vault", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "chronos"], "clients": {"grpc": ["dictionary", "cards-accounts", "payments", "colvir-bridge", "users", "notifications"]}}, {"name": "antifraud", "type": "logic", "description": "Service for antifraud", "dependencies": ["vault", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "chronos"], "clients": {"grpc": []}}, {"name": "crm", "type": "logic", "description": "Service for crm", "dependencies": ["vault", "postgresql", "grafana", "prometheus", "jaeger", "sentry"], "clients": {"grpc": ["users", "loans", "cards-accounts", "bts-bridge", "payments", "deposits", "pkb-bridge"]}}, {"name": "tokenize", "type": "logic", "description": "Tokenize cards", "dependencies": ["vault", "grafana", "prometheus", "postgresql"], "clients": {"grpc": ["processing-bridge"]}}, {"name": "balance-updater", "type": "logic", "description": "Balance updater service", "dependencies": ["vault", "kafka", "grafana", "prometheus", "postgresql"], "clients": {"grpc": ["processing-bridge"]}}, {"name": "ka<PERSON>i", "type": "bridge", "description": "Service for kaspi integrations", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}, {"name": "bitrix", "type": "bridge", "description": "Service for bitrix CRM integration", "dependencies": ["vault", "mongodb", "postgresql", "grafana", "prometheus", "jaeger", "sentry", "kafka"], "clients": {"grpc": []}}], "deploy": {"images": {"alpine": "alpine:3.20", "golang": "golang:1.23.1-alpine3.20", "kube": "registry.redmadrobot.com:5005/zaman/backend/zaman/helm-kubectl-docker:v1.21.1-v3.6.0", "base_srv": "registry.redmadrobot.com:5005/zaman/backend/zaman/kafka-golang-1.23.1:latest"}}}